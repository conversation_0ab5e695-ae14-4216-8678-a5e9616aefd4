import fs from 'fs-extra';
import path from 'path';
import os from 'os';
import yaml from 'yaml';
export class ConfigManager {
    configPath;
    config;
    constructor() {
        this.configPath = path.join(os.homedir(), '.arien-ai', 'config.yaml');
        this.config = this.getDefaultConfig();
    }
    getDefaultConfig() {
        return {
            currentProvider: 'deepseek',
            currentModel: 'deepseek-chat',
            providers: {},
            sessionHistory: [], // Deprecated - kept for backward compatibility
            workingDirectory: process.cwd(),
            autoApprove: false,
            maxRetries: 3,
            retryDelay: 1000,
            showToolDetails: false,
            showToolExecutionDetails: false,
            currentSessionId: null, // New field for current session tracking
            autoCreateSession: true // New field to control automatic session creation
        };
    }
    async loadConfig() {
        try {
            await fs.ensureDir(path.dirname(this.configPath));
            if (await fs.pathExists(this.configPath)) {
                const configData = await fs.readFile(this.configPath, 'utf-8');
                const loadedConfig = yaml.parse(configData);
                // Merge with defaults to ensure all properties exist
                this.config = { ...this.getDefaultConfig(), ...loadedConfig };
            }
            else {
                // Create default config file
                await this.saveConfig();
            }
        }
        catch (error) {
            console.error('Error loading config:', error);
            this.config = this.getDefaultConfig();
        }
        return this.config;
    }
    async saveConfig() {
        try {
            await fs.ensureDir(path.dirname(this.configPath));
            const configYaml = yaml.stringify(this.config, { indent: 2 });
            await fs.writeFile(this.configPath, configYaml, 'utf-8');
        }
        catch (error) {
            console.error('Error saving config:', error);
            throw error;
        }
    }
    getConfig() {
        return this.config;
    }
    async updateProvider(provider, config) {
        if (provider === 'deepseek') {
            this.config.providers[provider] = config;
        }
        else {
            this.config.providers[provider] = config;
        }
        this.config.currentProvider = provider;
        if (provider === 'deepseek' && config.models.length > 0) {
            this.config.currentModel = config.models[0];
        }
        else if (provider === 'ollama' && config.models.length > 0) {
            this.config.currentModel = config.models[0];
        }
        await this.saveConfig();
    }
    async updateCurrentModel(model) {
        this.config.currentModel = model;
        await this.saveConfig();
    }
    async updateCurrentProvider(provider) {
        if (this.config.providers[provider]) {
            this.config.currentProvider = provider;
            const providerConfig = this.config.providers[provider];
            if (providerConfig && providerConfig.models.length > 0) {
                this.config.currentModel = providerConfig.models[0];
            }
            await this.saveConfig();
        }
        else {
            throw new Error(`Provider ${provider} is not configured`);
        }
    }
    async addMessageToHistory(message) {
        // Deprecated method - kept for backward compatibility
        // New session management should use SessionManager instead
        this.config.sessionHistory.push(message);
        // Keep only last 100 messages to prevent config file from growing too large
        if (this.config.sessionHistory.length > 100) {
            this.config.sessionHistory = this.config.sessionHistory.slice(-100);
        }
        await this.saveConfig();
    }
    async clearHistory() {
        // Deprecated method - kept for backward compatibility
        // New session management should use SessionManager instead
        this.config.sessionHistory = [];
        await this.saveConfig();
    }
    async updateCurrentSession(sessionId) {
        this.config.currentSessionId = sessionId;
        await this.saveConfig();
    }
    async setAutoCreateSession(autoCreate) {
        this.config.autoCreateSession = autoCreate;
        await this.saveConfig();
    }
    getCurrentSessionId() {
        return this.config.currentSessionId || null;
    }
    shouldAutoCreateSession() {
        return this.config.autoCreateSession !== false; // Default to true
    }
    async updateWorkingDirectory(directory) {
        this.config.workingDirectory = directory;
        await this.saveConfig();
    }
    async toggleAutoApprove() {
        this.config.autoApprove = !this.config.autoApprove;
        await this.saveConfig();
        return this.config.autoApprove;
    }
    getCurrentProvider() {
        return this.config.providers[this.config.currentProvider];
    }
    isProviderConfigured(provider) {
        return !!this.config.providers[provider];
    }
    getConfigPath() {
        return this.configPath;
    }
    async toggleToolDetails() {
        this.config.showToolDetails = !this.config.showToolDetails;
        await this.saveConfig();
        return this.config.showToolDetails;
    }
    async toggleToolExecutionDetails() {
        this.config.showToolExecutionDetails = !this.config.showToolExecutionDetails;
        await this.saveConfig();
        return this.config.showToolExecutionDetails;
    }
    async setToolDetailsVisibility(showDetails, showExecutionDetails) {
        this.config.showToolDetails = showDetails;
        this.config.showToolExecutionDetails = showExecutionDetails;
        await this.saveConfig();
    }
}
//# sourceMappingURL=ConfigManager.js.map