import fs from 'fs-extra';
import path from 'path';
import os from 'os';
import { SessionData, SessionMetadata, ChatMessage } from '../types/index.js';

export class SessionManager {
  private sessionsDir: string;
  private currentSessionId: string | null = null;

  constructor() {
    this.sessionsDir = path.join(os.homedir(), '.arien-ai', 'sessions');
  }

  async initialize(): Promise<void> {
    await fs.ensureDir(this.sessionsDir);
  }

  async createNewSession(provider: string, model: string, workingDirectory: string, name?: string): Promise<string> {
    const sessionId = `session_${Date.now()}`;
    const now = new Date();
    
    const metadata: SessionMetadata = {
      id: sessionId,
      name: name || `Session ${new Date().toLocaleDateString()}`,
      createdAt: now,
      lastAccessedAt: now,
      messageCount: 0,
      provider,
      model,
      workingDirectory
    };

    const sessionData: SessionData = {
      metadata,
      messages: []
    };

    await this.saveSession(sessionData);
    this.currentSessionId = sessionId;
    
    return sessionId;
  }

  async loadSession(sessionId: string): Promise<SessionData | null> {
    try {
      const sessionPath = path.join(this.sessionsDir, `${sessionId}.json`);
      
      if (!(await fs.pathExists(sessionPath))) {
        return null;
      }

      const sessionData = await fs.readJSON(sessionPath) as SessionData;
      
      // Update last accessed time
      sessionData.metadata.lastAccessedAt = new Date();
      await this.saveSession(sessionData);
      
      this.currentSessionId = sessionId;
      return sessionData;
    } catch (error) {
      console.error(`Error loading session ${sessionId}:`, error);
      return null;
    }
  }

  async saveSession(sessionData: SessionData): Promise<void> {
    try {
      const sessionPath = path.join(this.sessionsDir, `${sessionData.metadata.id}.json`);
      
      // Update metadata
      sessionData.metadata.messageCount = sessionData.messages.length;
      sessionData.metadata.lastAccessedAt = new Date();
      
      await fs.writeJSON(sessionPath, sessionData, { spaces: 2 });
    } catch (error) {
      console.error(`Error saving session ${sessionData.metadata.id}:`, error);
      throw error;
    }
  }

  async addMessageToSession(sessionId: string, message: ChatMessage): Promise<void> {
    const sessionData = await this.loadSession(sessionId);
    if (!sessionData) {
      throw new Error(`Session ${sessionId} not found`);
    }

    sessionData.messages.push(message);
    await this.saveSession(sessionData);
  }

  async clearSession(sessionId: string): Promise<void> {
    const sessionData = await this.loadSession(sessionId);
    if (!sessionData) {
      throw new Error(`Session ${sessionId} not found`);
    }

    sessionData.messages = [];
    await this.saveSession(sessionData);
  }

  async deleteSession(sessionId: string): Promise<void> {
    try {
      const sessionPath = path.join(this.sessionsDir, `${sessionId}.json`);
      await fs.remove(sessionPath);
      
      if (this.currentSessionId === sessionId) {
        this.currentSessionId = null;
      }
    } catch (error) {
      console.error(`Error deleting session ${sessionId}:`, error);
      throw error;
    }
  }

  async listSessions(): Promise<SessionMetadata[]> {
    try {
      await this.initialize();
      const files = await fs.readdir(this.sessionsDir);
      const sessionFiles = files.filter(file => file.endsWith('.json'));
      
      const sessions: SessionMetadata[] = [];
      
      for (const file of sessionFiles) {
        try {
          const sessionPath = path.join(this.sessionsDir, file);
          const sessionData = await fs.readJSON(sessionPath) as SessionData;
          sessions.push(sessionData.metadata);
        } catch (error) {
          console.error(`Error reading session file ${file}:`, error);
        }
      }
      
      // Sort by last accessed time (most recent first)
      return sessions.sort((a, b) => 
        new Date(b.lastAccessedAt).getTime() - new Date(a.lastAccessedAt).getTime()
      );
    } catch (error) {
      console.error('Error listing sessions:', error);
      return [];
    }
  }

  async renameSession(sessionId: string, newName: string): Promise<void> {
    const sessionData = await this.loadSession(sessionId);
    if (!sessionData) {
      throw new Error(`Session ${sessionId} not found`);
    }

    sessionData.metadata.name = newName;
    await this.saveSession(sessionData);
  }

  getCurrentSessionId(): string | null {
    return this.currentSessionId;
  }

  async getSessionCount(): Promise<number> {
    const sessions = await this.listSessions();
    return sessions.length;
  }

  async exportSession(sessionId: string, exportPath: string): Promise<void> {
    const sessionData = await this.loadSession(sessionId);
    if (!sessionData) {
      throw new Error(`Session ${sessionId} not found`);
    }

    await fs.writeJSON(exportPath, sessionData, { spaces: 2 });
  }

  async importSession(importPath: string): Promise<string> {
    try {
      const sessionData = await fs.readJSON(importPath) as SessionData;
      
      // Generate new session ID to avoid conflicts
      const newSessionId = `session_${Date.now()}`;
      sessionData.metadata.id = newSessionId;
      sessionData.metadata.lastAccessedAt = new Date();
      
      await this.saveSession(sessionData);
      return newSessionId;
    } catch (error) {
      console.error('Error importing session:', error);
      throw error;
    }
  }

  async cleanupOldSessions(maxAge: number = 30): Promise<number> {
    const sessions = await this.listSessions();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - maxAge);
    
    let deletedCount = 0;
    
    for (const session of sessions) {
      if (new Date(session.lastAccessedAt) < cutoffDate) {
        await this.deleteSession(session.id);
        deletedCount++;
      }
    }
    
    return deletedCount;
  }
}
