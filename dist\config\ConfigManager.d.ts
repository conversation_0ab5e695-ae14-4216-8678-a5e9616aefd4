import { CliConfig, DeepseekConfig, OllamaConfig } from '../types/index.js';
export declare class ConfigManager {
    private configPath;
    private config;
    constructor();
    private getDefaultConfig;
    loadConfig(): Promise<CliConfig>;
    saveConfig(): Promise<void>;
    getConfig(): CliConfig;
    updateProvider(provider: 'deepseek' | 'ollama', config: DeepseekConfig | OllamaConfig): Promise<void>;
    updateCurrentModel(model: string): Promise<void>;
    updateCurrentProvider(provider: 'deepseek' | 'ollama'): Promise<void>;
    addMessageToHistory(message: any): Promise<void>;
    clearHistory(): Promise<void>;
    updateCurrentSession(sessionId: string | null): Promise<void>;
    setAutoCreateSession(autoCreate: boolean): Promise<void>;
    getCurrentSessionId(): string | null;
    shouldAutoCreateSession(): boolean;
    updateWorkingDirectory(directory: string): Promise<void>;
    toggleAutoApprove(): Promise<boolean>;
    getCurrentProvider(): DeepseekConfig | OllamaConfig | undefined;
    isProviderConfigured(provider: 'deepseek' | 'ollama'): boolean;
    getConfigPath(): string;
    toggleToolDetails(): Promise<boolean>;
    toggleToolExecutionDetails(): Promise<boolean>;
    setToolDetailsVisibility(showDetails: boolean, showExecutionDetails: boolean): Promise<void>;
}
//# sourceMappingURL=ConfigManager.d.ts.map