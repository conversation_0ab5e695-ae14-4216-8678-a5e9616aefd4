import axios, { AxiosInstance } from 'axios';
import { Chat<PERSON><PERSON>age, ToolResult, DeepseekConfig } from '../types/index.js';
import { ErrorHandler } from '../utils/ErrorHandler.js';
import { ShellTool } from '../tools/ShellTool.js';
import { getSystemPrompt } from '../prompts/SystemPrompt.js';

export class DeepseekProvider {
  private client: AxiosInstance;
  private config: DeepseekConfig;
  private errorHandler: ErrorHandler;
  private shellTool: ShellTool;

  constructor(config: DeepseekConfig) {
    this.config = config;
    this.errorHandler = new ErrorHandler();
    this.shellTool = new ShellTool();
    
    this.client = axios.create({
      baseURL: config.baseUrl,
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 120000, // Increased to 2 minutes
      validateStatus: (status) => status < 500, // Don't throw for 4xx errors, let us handle them
      maxRedirects: 5
    });
  }

  async chat(messages: ChatMessage[], model?: string): Promise<ChatMessage> {
    const selectedModel = model || this.config.models[0];

    // Separate API call from tool execution to prevent tool re-execution on retries
    const apiResponse = await this.errorHandler.executeWithRetry(async () => {
      // Add system prompt if not present
      const formattedMessages = this.formatMessages(messages);
      if (formattedMessages.length === 0 || formattedMessages[0].role !== 'system') {
        formattedMessages.unshift({
          role: 'system',
          content: getSystemPrompt()
        });
      }



      const requestPayload = {
        model: selectedModel,
        messages: formattedMessages,
        tools: this.getAvailableTools(),
        tool_choice: 'auto',
        temperature: 0.7,
        max_tokens: 4000,
        stream: false
      };

      const response = await this.client.post('/chat/completions', requestPayload);

      // Handle HTTP error responses
      if (response.status >= 400) {
        const errorData = response.data;
        let errorMessage = `Deepseek API error (${response.status})`;

        if (errorData && errorData.error) {
          errorMessage += `: ${errorData.error.message || errorData.error}`;
        }

        if (response.status === 401) {
          errorMessage += '\n💡 Please check your API key configuration.';
        } else if (response.status === 429) {
          errorMessage += '\n💡 Rate limit exceeded. Please wait before making another request.';
        } else if (response.status >= 500) {
          errorMessage += '\n💡 Server error. This is usually temporary.';
        }

        throw new Error(errorMessage);
      }

      const assistantMessage = response.data.choices[0]?.message;
      if (!assistantMessage) {
        throw new Error('No response from Deepseek API - empty response received');
      }

      return assistantMessage;
    }, 'Deepseek API call');

    // Handle tool calls if present (outside retry logic to prevent re-execution)
    if (apiResponse.tool_calls && apiResponse.tool_calls.length > 0) {
      const toolResults = await this.executeToolCalls(apiResponse.tool_calls);

      return {
        id: this.generateId(),
        role: 'assistant',
        content: apiResponse.content || '',
        timestamp: new Date(),
        toolCalls: apiResponse.tool_calls.map((tc: any) => ({
          id: tc.id,
          name: tc.function.name,
          arguments: JSON.parse(tc.function.arguments)
        })),
        toolResults
      };
    }

    return {
      id: this.generateId(),
      role: 'assistant',
      content: apiResponse.content || '',
      timestamp: new Date()
    };
  }

  private formatMessages(messages: ChatMessage[]): any[] {
    const formattedMessages: any[] = [];

    for (const msg of messages) {
      // Skip messages that are malformed or have invalid roles
      if (!msg.role || !['system', 'user', 'assistant'].includes(msg.role)) {
        continue;
      }

      if (msg.role === 'user' || msg.role === 'system') {
        // Simple user/system messages
        formattedMessages.push({
          role: msg.role,
          content: msg.content || ''
        });
      } else if (msg.role === 'assistant') {
        // Assistant messages
        const assistantMsg: any = {
          role: 'assistant',
          content: msg.content || ''
        };

        // Add tool calls if present
        if (msg.toolCalls && msg.toolCalls.length > 0) {
          assistantMsg.tool_calls = msg.toolCalls.map(tc => ({
            id: tc.id,
            type: 'function',
            function: {
              name: tc.name,
              arguments: JSON.stringify(tc.arguments)
            }
          }));
        }

        formattedMessages.push(assistantMsg);

        // Add tool results as separate tool messages if present
        if (msg.toolResults && msg.toolResults.length > 0) {
          for (const tr of msg.toolResults) {
            formattedMessages.push({
              role: 'tool',
              tool_call_id: tr.id,
              content: JSON.stringify({
                success: tr.success,
                result: tr.result,
                error: tr.error,
                executionTime: tr.executionTime
              })
            });
          }
        }
      }
    }

    return formattedMessages;
  }

  private getAvailableTools(): any[] {
    const shellToolDef = ShellTool.getToolDefinition();
    
    return [
      {
        type: 'function',
        function: {
          name: shellToolDef.name,
          description: shellToolDef.description,
          parameters: shellToolDef.parameters
        }
      }
    ];
  }

  private async executeToolCalls(toolCalls: any[]): Promise<ToolResult[]> {
    const results: ToolResult[] = [];
    
    for (const toolCall of toolCalls) {
      const startTime = Date.now();
      
      try {
        let result: any;
        
        if (toolCall.function.name === 'execute_shell_command') {
          const args = JSON.parse(toolCall.function.arguments);
          result = await this.shellTool.execute({
            command: args.command,
            args: args.args,
            cwd: args.cwd,
            timeout: args.timeout,
            requireApproval: args.requireApproval
          });
        } else {
          throw new Error(`Unknown tool: ${toolCall.function.name}`);
        }
        
        results.push({
          id: toolCall.id,
          name: toolCall.function.name,
          result,
          success: true,
          executionTime: Date.now() - startTime
        });
        
      } catch (error) {
        results.push({
          id: toolCall.id,
          name: toolCall.function.name,
          result: null,
          success: false,
          error: (error as Error).message,
          executionTime: Date.now() - startTime
        });
      }
    }
    
    return results;
  }

  async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.get('/models');
      return response.data.data
        .filter((model: any) => model.id.includes('deepseek'))
        .map((model: any) => model.id);
    } catch (error) {
      this.errorHandler.logWarning('Could not fetch models from Deepseek API, using default models');
      return this.config.models;
    }
  }

  async validateConnection(): Promise<boolean> {
    try {
      const response = await this.client.get('/models', { timeout: 10000 });
      return response.status === 200;
    } catch (error) {
      this.errorHandler.logWarning(`Connection validation failed: ${(error as Error).message}`);
      return false;
    }
  }

  async testConnection(): Promise<{ success: boolean; error?: string; latency?: number }> {
    const startTime = Date.now();
    try {
      const response = await this.client.get('/models', { timeout: 10000 });
      const latency = Date.now() - startTime;

      if (response.status === 200) {
        return { success: true, latency };
      } else {
        return { success: false, error: `HTTP ${response.status}: ${response.statusText}` };
      }
    } catch (error) {
      const latency = Date.now() - startTime;
      return {
        success: false,
        error: (error as Error).message,
        latency
      };
    }
  }

  private generateId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  updateConfig(config: DeepseekConfig): void {
    this.config = config;
    this.client.defaults.headers['Authorization'] = `Bearer ${config.apiKey}`;
    this.client.defaults.baseURL = config.baseUrl;
  }

  getConfig(): DeepseekConfig {
    return { ...this.config };
  }
}
