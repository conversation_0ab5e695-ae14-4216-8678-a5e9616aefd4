import { ConfigManager } from '../../config/ConfigManager.js';
import { SessionManager } from '../../session/SessionManager.js';
import { SlashCommand, TerminalState } from '../../types/index.js';
export declare class SlashCommandsComponent {
    private configManager;
    private sessionManager;
    private state;
    private messageHistoryComponent;
    constructor(configManager: ConfigManager, state: TerminalState, sessionManager: SessionManager);
    getCommands(): SlashCommand[];
    executeCommand(input: string): Promise<boolean>;
    private handleModelCommand;
    private handleProviderCommand;
    private handleSessionCommand;
    private startNewSession;
    private clearSession;
    private saveSession;
    private listSessions;
    private switchSession;
    private deleteSession;
    private renameSession;
    private exportSession;
    private importSession;
    private cleanupSessions;
    private handleHistoryCommand;
    private handleSystemCommand;
    private handleDebugCommand;
    private handleTestCommand;
    private handleHelpCommand;
    private handleExitCommand;
    isSlashCommand(input: string): boolean;
}
//# sourceMappingURL=SlashCommandsComponent.d.ts.map