import { SessionData, SessionMetadata, ChatMessage } from '../types/index.js';
export declare class SessionManager {
    private sessionsDir;
    private currentSessionId;
    constructor();
    initialize(): Promise<void>;
    createNewSession(provider: string, model: string, workingDirectory: string, name?: string): Promise<string>;
    loadSession(sessionId: string): Promise<SessionData | null>;
    saveSession(sessionData: SessionData): Promise<void>;
    addMessageToSession(sessionId: string, message: ChatMessage): Promise<void>;
    clearSession(sessionId: string): Promise<void>;
    deleteSession(sessionId: string): Promise<void>;
    listSessions(): Promise<SessionMetadata[]>;
    renameSession(sessionId: string, newName: string): Promise<void>;
    getCurrentSessionId(): string | null;
    getSessionCount(): Promise<number>;
    exportSession(sessionId: string, exportPath: string): Promise<void>;
    importSession(importPath: string): Promise<string>;
    cleanupOldSessions(maxAge?: number): Promise<number>;
}
//# sourceMappingURL=SessionManager.d.ts.map