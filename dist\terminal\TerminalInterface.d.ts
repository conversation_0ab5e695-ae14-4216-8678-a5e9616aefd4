export declare class TerminalInterface {
    private configManager;
    private sessionManager;
    private state;
    private deepseekProvider?;
    private ollamaProvider?;
    private slashCommands;
    private messageHistory;
    private chatInput;
    private thinkingAnimation;
    private errorHandler;
    private rl?;
    constructor();
    start(): Promise<void>;
    private updateStateFromConfig;
    private initializeSession;
    private initializeProviders;
    private startInteractiveMode;
    private handleInput;
    private handleChatMessage;
    private handleApprovalRequired;
    private getCurrentProvider;
    private getPrompt;
    private getCurrentDirectoryName;
    private generateId;
    stop(): Promise<void>;
}
//# sourceMappingURL=TerminalInterface.d.ts.map